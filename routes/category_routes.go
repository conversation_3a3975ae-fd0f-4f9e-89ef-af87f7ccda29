package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"damoncoo/coremovie/middleware"
	"damoncoo/coremovie/models"
	"damoncoo/coremovie/services"
)

// CategoryRoutes handles category-related HTTP routes
type CategoryRoutes struct {
	categoryService *services.CategoryService
}

// NewCategoryRoutes creates a new CategoryRoutes instance
func NewCategoryRoutes() *CategoryRoutes {
	return &CategoryRoutes{
		categoryService: services.NewCategoryService(),
	}
}

// RegisterRoutes registers all category routes
func (r *CategoryRoutes) RegisterRoutes(router *gin.RouterGroup) {
	// Public routes (for frontend to get categories)
	categories := router.Group("/categories")
	{
		categories.GET("", r.GetCategories)
		categories.GET("/active", r.GetActiveCategories)
		categories.GET("/:id", r.GetCategory)
	}

	// Protected routes (require authentication)
	protected := categories.Group("")
	protected.Use(middleware.AuthMiddleware())
	{
		protected.POST("", middleware.AdminMiddleware(), r.CreateCategory)
		protected.PUT("/:id", middleware.AdminMiddleware(), r.UpdateCategory)
		protected.DELETE("/:id", middleware.AdminMiddleware(), r.DeleteCategory)
	}
}

// GetCategories handles getting all categories with pagination
func (r *CategoryRoutes) GetCategories(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "0")) // 0 means no limit
	includeStats := c.DefaultQuery("include_stats", "false") == "true"

	if page < 1 {
		page = 1
	}
	if limit < 0 {
		limit = 0
	}

	categories, total, err := r.categoryService.GetAllCategories(page, limit, includeStats)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response := gin.H{
		"success": true,
		"data": gin.H{
			"categories": categories,
		},
	}

	// Add pagination info if limit was specified
	if limit > 0 {
		totalPages := (total + int64(limit) - 1) / int64(limit)
		response["data"].(gin.H)["pagination"] = gin.H{
			"current_page": page,
			"total_pages":  totalPages,
			"total_items":  total,
			"limit":        limit,
		}
	}

	c.JSON(http.StatusOK, response)
}

// GetActiveCategories retrieves all active categories
// @Summary Get active categories
// @Description Get all active categories
// @Tags Categories
// @Accept json
// @Produce json
// @Success 200 {object} []models.Category
// @Failure 500 {object} models.ErrorResponse
// @Router /categories/active [get]
func (r *CategoryRoutes) GetActiveCategories(c *gin.Context) {
	categories, err := r.categoryService.GetActiveCategories()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// GetCategory handles getting a single category by ID
func (r *CategoryRoutes) GetCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	includeStats := c.DefaultQuery("include_stats", "false") == "true"

	category, err := r.categoryService.GetCategoryByID(id, includeStats)
	if err != nil {
		if err.Error() == "category not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// CreateCategory handles creating a new category (admin only)
func (r *CategoryRoutes) CreateCategory(c *gin.Context) {
	var req models.CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category, err := r.categoryService.CreateCategory(&req)
	if err != nil {
		if err.Error() == "category name already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    category,
	})
}

// UpdateCategory handles updating a category (admin only)
func (r *CategoryRoutes) UpdateCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req models.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category, err := r.categoryService.UpdateCategory(id, &req)
	if err != nil {
		if err.Error() == "category not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "category name already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// DeleteCategory handles deleting a category (admin only)
func (r *CategoryRoutes) DeleteCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	err = r.categoryService.DeleteCategory(id)
	if err != nil {
		if err.Error() == "category not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "cannot delete category with existing videos" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Category deleted successfully",
	})
}

// GetCategoryStats handles getting category statistics
func (r *CategoryRoutes) GetCategoryStats(c *gin.Context) {
	stats, err := r.categoryService.GetCategoryStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
