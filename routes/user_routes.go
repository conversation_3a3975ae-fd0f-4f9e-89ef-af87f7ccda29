package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"damoncoo/coremovie/middleware"
	"damoncoo/coremovie/models"
	"damoncoo/coremovie/services"
)

// UserRoutes handles user-related HTTP routes
type UserRoutes struct {
	userService *services.UserService
}

// NewUserRoutes creates a new UserRoutes instance with an external UserService
func NewUserRoutes(service *services.UserService) *UserRoutes {
	return &UserRoutes{
		userService: service,
	}
}

// RegisterRoutes registers all user routes
func (r *UserRoutes) RegisterRoutes(router *gin.RouterGroup) {
	// Public routes
	auth := router.Group("/auth")
	{
		auth.POST("/login", r.Login)
	}

	// Protected routes
	users := router.Group("/users")
	users.Use(middleware.AuthMiddleware())
	{
		users.GET("", r.GetUsers)
		users.GET("/:id", r.GetUser)
		users.PUT("/:id", r.UpdateUser)
		users.DELETE("/:id", r.DeleteUser)
		users.GET("/stats", r.GetUserStats)
	}

	// Admin only routes
	adminUsers := router.Group("/admin/users")
	adminUsers.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		adminUsers.POST("", r.CreateUser)
	}
}

// Login handles user authentication
func (r *UserRoutes) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := r.userService.Login(&req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetUsers handles getting all users with pagination
func (r *UserRoutes) GetUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	users, total, err := r.userService.GetAllUsers(page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := (total + int64(limit) - 1) / int64(limit)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users":       users,
			"pagination": gin.H{
				"current_page": page,
				"total_pages":  totalPages,
				"total_items":  total,
				"limit":        limit,
			},
		},
	})
}

// GetUser handles getting a single user by ID
func (r *UserRoutes) GetUser(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	user, err := r.userService.GetUserByID(id)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// CreateUser handles creating a new user (admin only)
func (r *UserRoutes) CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user, err := r.userService.CreateUser(&req)
	if err != nil {
		if err.Error() == "email already exists" || err.Error() == "username already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    user,
	})
}

// UpdateUser handles updating a user
func (r *UserRoutes) UpdateUser(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Check if user can update this profile
	currentUserID, _ := middleware.GetCurrentUserID(c)
	currentUser, _ := middleware.GetCurrentUser(c)
	
	// Users can only update their own profile unless they're admin
	if currentUserID != id && currentUser.Role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only update your own profile"})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Non-admin users cannot change role or status
	if currentUser.Role != "admin" {
		req.Role = ""
		req.Status = ""
	}

	user, err := r.userService.UpdateUser(id, &req)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "email already exists" || err.Error() == "username already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// DeleteUser handles deleting a user (admin only)
func (r *UserRoutes) DeleteUser(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Prevent admin from deleting themselves
	currentUserID, _ := middleware.GetCurrentUserID(c)
	if currentUserID == id {
		c.JSON(http.StatusBadRequest, gin.H{"error": "You cannot delete your own account"})
		return
	}

	err = r.userService.DeleteUser(id)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User deleted successfully",
	})
}

// GetUserStats handles getting user statistics
func (r *UserRoutes) GetUserStats(c *gin.Context) {
	stats, err := r.userService.GetUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
