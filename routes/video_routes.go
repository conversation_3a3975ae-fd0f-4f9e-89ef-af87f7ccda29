package routes

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"

	"damoncoo/coremovie/middleware"
	"damoncoo/coremovie/models"
	"damoncoo/coremovie/services"
	"damoncoo/coremovie/storage"
	"damoncoo/coremovie/utils"

)

// VideoRoutes handles video-related HTTP routes
type VideoRoutes struct {
	videoService *services.VideoService
}

// NewVideoRoutes creates a new VideoRoutes instance
func NewVideoRoutes() *VideoRoutes {
	return &VideoRoutes{
		videoService: services.NewVideoService(),
	}
}

// RegisterRoutes registers all video routes
func (r *VideoRoutes) RegisterRoutes(router *gin.RouterGroup, root *gin.RouterGroup) {
	// Public routes
	videos := router.Group("/videos")
	{
		videos.GET("", r.GetVideos)
		videos.GET("/:id", r.GetVideo)
		videos.POST("/detail", r.GetVideo) // New endpoint for getting video details with array of objects
		videos.POST("/:id/view", r.IncrementViews)
		videos.GET("/stats", r.GetVideoStats)
		videos.PUT("/:id/signed-upload", r.UploadVideoWithSignedURL) // New signed URL upload endpoint
	}

	// Protected routes
	protectedVideos := router.Group("/videos")
	protectedVideos.Use(middleware.AuthMiddleware())
	{
		protectedVideos.POST("", r.CreateVideo)
		protectedVideos.POST("/presigned-url", r.GeneratePresignedURL)
		protectedVideos.PUT("/:id/upload", r.UploadVideo)
		protectedVideos.PUT("/:id", r.UpdateVideo)
		protectedVideos.DELETE("/:id", r.DeleteVideo)
	}

	// Configure static file serving based on storage type
	storageType := os.Getenv("STORAGE_TYPE")
	if storageType == "" {
		storageType = "local"
	}

	// Only register local file serving if using local storage
	if storage.StorageType(storageType) == storage.LocalStorage {
		uploadPath := os.Getenv("UPLOAD_PATH")
		root.Static("/uploads", uploadPath)
	}
}

// GetVideos handles getting all videos with pagination and filtering
func (r *VideoRoutes) GetVideos(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "12"))

	// Parse filter parameters
	categoryID, _ := strconv.ParseInt(c.DefaultQuery("category_id", "0"), 10, 64)
	status := c.DefaultQuery("status", "")
	userID, _ := strconv.ParseInt(c.DefaultQuery("user_id", "0"), 10, 64)

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 12
	}

	videos, total, err := r.videoService.GetAllVideos(page, limit, categoryID, status, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := (total + int64(limit) - 1) / int64(limit)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"videos": videos,
			"pagination": gin.H{
				"current_page": page,
				"total_pages":  totalPages,
				"total_items":  total,
				"limit":        limit,
			},
		},
	})
}

// GetVideo handles getting a single video by an array of key-value pairs
func (r *VideoRoutes) GetVideo(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Extract ID from the request object
	var id int64
	if idVal, exists := req["id"]; exists {
		switch val := idVal.(type) {
		case float64:
			id = int64(val)
		case string:
			if parsedID, err := strconv.ParseInt(val, 10, 64); err == nil {
				id = parsedID
			} else {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID format"})
				return
			}
		case int64:
			id = val
		default:
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID format"})
			return
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Video ID is required"})
		return
	}

	video, err := r.videoService.GetVideoByID(id)
	if err != nil {
		if err.Error() == "video not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    video,
	})
}

// CreateVideo handles creating a new video
func (r *VideoRoutes) CreateVideo(c *gin.Context) {
	var req models.CreateVideoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	video, err := r.videoService.CreateVideo(&req, userID)
	if err != nil {
		if err.Error() == "category not found" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    video,
	})
}

// UploadVideo handles video file upload
func (r *VideoRoutes) UploadVideo(c *gin.Context) {

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	// Get the uploaded file
	file, err := c.FormFile("video")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No video file provided"})
		return
	}

	// Check file size (limit to 500MB)
	maxSize := int64(500 * 1024 * 1024) // 500MB
	if file.Size > maxSize {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size too large (max 500MB)"})
		return
	}

	err = r.videoService.UploadVideoFile(id, file)
	if err != nil {
		if err.Error() == "video not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "invalid video file type" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Video uploaded successfully",
	})
}

// UploadVideoWithSignedURL handles video file upload using a signed URL
// This handles both S3 direct uploads and local storage uploads with the same interface
func (r *VideoRoutes) UploadVideoWithSignedURL(c *gin.Context) {
	// Get video ID from URL
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	// Read the raw file data from the request body
	fileData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
		return
	}
	defer c.Request.Body.Close()

	// Get content type from header
	contentType := c.GetHeader("Content-Type")
	if contentType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Content-Type header is required"})
		return
	}

	// Check file size limit (500MB)
	maxSize := int64(500 * 1024 * 1024) // 500MB
	if int64(len(fileData)) > maxSize {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size too large (max 500MB)"})
		return
	}

	// For signed URL uploads, we need to validate the signed URL parameters
	// and get the filename from the query parameters
	signedURLConfig := utils.NewSignedURLConfig()
	params, err := signedURLConfig.ValidateSignedURL(c.Request.URL.Query())
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid signed URL: %s", err.Error())})
		return
	}

	// Verify video ID from URL matches the signed parameters
	if id != params.VideoID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Video ID mismatch"})
		return
	}

	// Check file size matches signed parameters
	if int64(len(fileData)) != params.FileSize {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size mismatch"})
		return
	}

	// Upload the file using the video service
	err = r.videoService.UploadVideoFileFromBytes(id, fileData, params.Filename, contentType, params.FileSize)
	if err != nil {
		if err.Error() == "video not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "invalid video file type" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Video uploaded successfully",
	})
}

// UpdateVideo handles updating a video
func (r *VideoRoutes) UpdateVideo(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	var req models.UpdateVideoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	currentUser, _ := middleware.GetCurrentUser(c)
	isAdmin := currentUser.Role == "admin"

	video, err := r.videoService.UpdateVideo(id, &req, userID, isAdmin)
	if err != nil {
		if err.Error() == "video not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "category not found" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else if err.Error() == "you can only update your own videos" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    video,
	})
}

// DeleteVideo handles deleting a video
func (r *VideoRoutes) DeleteVideo(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	currentUser, _ := middleware.GetCurrentUser(c)
	isAdmin := currentUser.Role == "admin"

	err = r.videoService.DeleteVideo(id, userID, isAdmin)
	if err != nil {
		if err.Error() == "video not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else if err.Error() == "you can only delete your own videos" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Video deleted successfully",
	})
}

// GeneratePresignedURL handles generating a presigned URL for direct S3 upload
func (r *VideoRoutes) GeneratePresignedURL(c *gin.Context) {
	var req models.PresignedURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify user owns the video
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Get video to check ownership
	video, err := r.videoService.GetVideoByID(req.VideoID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	if video.UserID != userID {
		currentUser, _ := middleware.GetCurrentUser(c)
		isAdmin := currentUser.Role == "admin"
		if !isAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "You can only generate presigned URLs for your own videos"})
			return
		}
	}

	presignedURL, err := r.videoService.GeneratePresignedURL(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    presignedURL,
	})
}

// IncrementViews handles incrementing video view count
func (r *VideoRoutes) IncrementViews(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	err = r.videoService.IncrementViews(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "View count updated",
	})
}

// GetVideoStats handles getting video statistics
func (r *VideoRoutes) GetVideoStats(c *gin.Context) {
	stats, err := r.videoService.GetVideoStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
