# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=coremovie

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key

# Server Configuration
PORT=8080
GIN_MODE=debug
ALLOW_ORIGINS=http://localhost:7300 # Comma-separated list of allowed origins

# Storage Configuration
# Set to "local" for local file storage or "s3" for S3-compatible storage
STORAGE_TYPE=local

# File Upload Configuration
MAX_UPLOAD_SIZE=500MB
UPLOAD_PATH=./uploads
LOCAL_BASE_URL=http://localhost:8080
SIGNED_URL_SECRET=your-signed-url-secret-key

# S3 Configuration (only needed if STORAGE_TYPE=s3)
# S3_ENDPOINT=
# S3_REGION=us-east-1
# S3_ACCESS_KEY_ID=
# S3_SECRET_ACCESS_KEY=
# S3_BUCKET_NAME=
# S3_USE_SSL=true
# S3_ACCESS_URL=custom-access-url
