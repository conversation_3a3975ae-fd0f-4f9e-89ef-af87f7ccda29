# CoreMovie - Vortex Video Management System

A modern, full-stack video management system built with Go, React, and MySQL. Features a beautiful dashboard interface for managing videos, users, and categories with role-based access control.

## ✨ Features

### 🎥 Video Management
- Upload and manage video files (MP4, AVI, MOV, WMV, WebM)
- **Signed URL uploads** - Secure, time-limited upload URLs (like AWS S3)
- Automatic video processing and thumbnail generation
- Category-based organization
- View tracking and analytics
- Video player with modal interface
- Bulk operations and filtering

### 👥 User Management
- User authentication with JWT tokens
- Role-based access control (Admin/User)
- User profile management
- Activity tracking

### 🏷️ Category Management
- Create and manage video categories
- Custom icons and colors
- Category statistics
- Hierarchical organization

### 📊 Dashboard & Analytics
- Real-time statistics
- Storage usage monitoring
- Recent activity tracking
- Visual charts and metrics

## 🛠️ Technology Stack

### Backend
- **Go 1.24+** - High-performance backend
- **Gin** - HTTP web framework
- **XORM** - ORM for database operations
- **MySQL** - Primary database
- **JWT** - Authentication tokens
- **bcrypt** - Password hashing

### Frontend
- **React 18** with TypeScript
- **Redux Toolkit** - State management
- **Tailwind CSS** - Utility-first styling
- **Axios** - HTTP client
- **Font Awesome** - Icons

## 📁 Project Structure

```
CoreMovie/
├── database/           # Database connection and migrations
├── middleware/         # Authentication and CORS middleware
├── models/            # Data models and structures
├── routes/            # HTTP route handlers
├── services/          # Business logic layer
├── frontend/          # React application
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── store/         # Redux store and slices
│   │   ├── api/           # API client functions
│   │   ├── types/         # TypeScript type definitions
│   │   └── App.tsx        # Main application component
├── uploads/           # File storage directory
├── main.go           # Application entry point
└── go.mod            # Go module definition
```

## 🚀 Quick Start

### Prerequisites
- Go 1.24 or higher
- Node.js 16+ and npm
- MySQL 8.0+

### Backend Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd CoreMovie
```

2. **Install Go dependencies**
```bash
go mod tidy
```

3. **Set up MySQL database**
```sql
CREATE DATABASE coremovie;
CREATE USER 'coremovie'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON coremovie.* TO 'coremovie'@'localhost';
FLUSH PRIVILEGES;
```

4. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your database credentials
```

5. **Start the backend server**
```bash
go run main.go
```

The backend will start on `http://localhost:8080`

### Frontend Setup

1. **Navigate to frontend directory**
```bash
cd frontend
```

2. **Install dependencies**
```bash
npm install
```

3. **Start the development server**
```bash
npm start
```

The frontend will start on `http://localhost:3000`

## 🔐 Default Credentials

After the first run, the system creates a default admin user:

- **Email:** <EMAIL>
- **Password:** password

⚠️ **Important:** Change these credentials immediately in production!

## 📝 Environment Variables

### Backend (.env)
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=coremovie
DB_PASSWORD=your_password
DB_NAME=coremovie

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Server Configuration
PORT=8080
GIN_MODE=debug

# File Upload Configuration
MAX_UPLOAD_SIZE=500MB
UPLOAD_PATH=./uploads

# Storage Configuration
STORAGE_TYPE=local
LOCAL_BASE_URL=http://localhost:8080

# Signed URL Configuration (optional, defaults to JWT_SECRET)
SIGNED_URL_SECRET=your-signed-url-secret-key-change-this-in-production
```

### Frontend (.env)
```env
REACT_APP_API_URL=http://localhost:8080/api/v1
```

## 🎯 API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login

### Videos
- `GET /api/v1/videos` - List videos with pagination
- `POST /api/v1/videos` - Create new video
- `GET /api/v1/videos/:id` - Get video details
- `PUT /api/v1/videos/:id` - Update video
- `DELETE /api/v1/videos/:id` - Delete video
- `POST /api/v1/videos/:id/upload` - Upload video file (traditional)
- `POST /api/v1/videos/presigned-url` - Generate signed URL for upload
- `PUT /api/v1/videos/:id/signed-upload` - Upload video using signed URL

### Categories
- `GET /api/v1/categories` - List categories
- `POST /api/v1/admin/categories` - Create category (Admin)
- `PUT /api/v1/admin/categories/:id` - Update category (Admin)
- `DELETE /api/v1/admin/categories/:id` - Delete category (Admin)

### Users
- `GET /api/v1/users` - List users
- `POST /api/v1/admin/users` - Create user (Admin)
- `PUT /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user (Admin)

## 🔧 Development

### Running Tests
```bash
# Backend tests
go test ./...

# Frontend tests
cd frontend
npm test
```

### Building for Production
```bash
# Build frontend
cd frontend
npm run build

# Build backend
go build -o coremovie main.go
```

## 🔐 Signed URL Upload System

CoreMovie includes a **signed URL upload system** that works similar to AWS S3, but for local storage. This provides secure, time-limited upload URLs that enhance security and user experience.

### How It Works

1. **Request Upload URL**: Client requests a signed URL for uploading a video
2. **Generate Signed URL**: Server generates a time-limited, cryptographically signed URL
3. **Direct Upload**: Client uploads the file directly using the signed URL
4. **Validation**: Server validates the signature and time expiration before accepting the upload

### Benefits

- **Security**: URLs are cryptographically signed and time-limited (15 minutes)
- **Performance**: Direct upload without additional authentication checks
- **S3-like Experience**: Familiar workflow for developers used to AWS S3
- **File Integrity**: File size and type validation built into the signature

### Usage Flow

```javascript
// 1. Request a signed URL
const response = await fetch('/api/v1/videos/presigned-url', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer <token>' },
  body: JSON.stringify({
    video_id: 123,
    filename: 'my-video.mp4',
    file_size: 10485760,
    file_type: 'video/mp4'
  })
});

const { url } = await response.json();

// 2. Upload directly using the signed URL
const formData = new FormData();
formData.append('video', file);

await fetch(url, {
  method: 'PUT',
  body: formData
});
```

### Configuration

The system uses HMAC-SHA256 signatures with a configurable secret key:

- `SIGNED_URL_SECRET`: Custom secret for signing URLs (optional)
- Falls back to `JWT_SECRET` if not specified
- URLs expire after 15 minutes by default

## 📱 Features Overview

### Dashboard
- Real-time statistics and metrics
- Recent video activity
- Storage usage monitoring
- Quick access to key functions

### Video Management
- Drag-and-drop file upload
- Video preview and playback
- Batch operations
- Advanced filtering and search
- Category-based organization

### User Management (Admin)
- User creation and management
- Role assignment
- Activity monitoring
- Bulk operations

### Category Management
- Visual category creation
- Custom colors and icons
- Usage statistics
- Hierarchical organization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

If you encounter any issues or have questions, please create an issue in the repository.

## 🙏 Acknowledgments

- Built with modern web technologies
- Inspired by popular video management platforms
- Community-driven development
