package services

import (
	"fmt"

	"damoncoo/coremovie/database"
	"damoncoo/coremovie/middleware"
	"damoncoo/coremovie/models"
)

// UserService handles user-related business logic
type UserService struct{}

// NewUserService creates a new UserService instance
func NewUserService() *UserService {
	return &UserService{}
}

// GetAllUsers retrieves all users with pagination
func (s *UserService) GetAllUsers(page, limit int) ([]models.UserResponse, int64, error) {
	var users []models.User
	var total int64

	// Get total count
	total, err := database.DB.Count(&models.User{})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %v", err)
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get users with pagination
	err = database.DB.Limit(limit, offset).OrderBy("created_at DESC").Find(&users)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %v", err)
	}

	// Convert to response format
	var userResponses []models.UserResponse
	for _, user := range users {
		userResponses = append(userResponses, user.ToResponse())
	}

	return userResponses, total, nil
}

// GetUserByID retrieves a user by ID
func (s *UserService) GetUserByID(id int64) (*models.UserResponse, error) {
	var user models.User
	has, err := database.DB.ID(id).Get(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("user not found")
	}

	response := user.ToResponse()
	return &response, nil
}

// CreateUser creates a new user
func (s *UserService) CreateUser(req *models.CreateUserRequest) (*models.UserResponse, error) {
	// Check if email already exists
	var existingUser models.User
	has, err := database.DB.Where("email = ?", req.Email).Get(&existingUser)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing email: %v", err)
	}
	if has {
		return nil, fmt.Errorf("email already exists")
	}

	// Check if username already exists
	has, err = database.DB.Where("username = ?", req.Username).Get(&existingUser)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing username: %v", err)
	}
	if has {
		return nil, fmt.Errorf("username already exists")
	}

	// Hash password
	hashedPassword, err := middleware.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %v", err)
	}

	// Set default role if not provided
	role := req.Role
	if role == "" {
		role = "user"
	}

	// Create user
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Role:     role,
		Status:   "active",
	}

	_, err = database.DB.Insert(user)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %v", err)
	}

	response := user.ToResponse()
	return &response, nil
}

// UpdateUser updates an existing user
func (s *UserService) UpdateUser(id int64, req *models.UpdateUserRequest) (*models.UserResponse, error) {
	// Get existing user
	var user models.User
	has, err := database.DB.ID(id).Get(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("user not found")
	}

	// Update fields if provided
	if req.Username != "" {
		// Check if username already exists (excluding current user)
		var existingUser models.User
		has, err := database.DB.Where("username = ? AND id != ?", req.Username, id).Get(&existingUser)
		if err != nil {
			return nil, fmt.Errorf("failed to check existing username: %v", err)
		}
		if has {
			return nil, fmt.Errorf("username already exists")
		}
		user.Username = req.Username
	}

	if req.Email != "" {
		// Check if email already exists (excluding current user)
		var existingUser models.User
		has, err := database.DB.Where("email = ? AND id != ?", req.Email, id).Get(&existingUser)
		if err != nil {
			return nil, fmt.Errorf("failed to check existing email: %v", err)
		}
		if has {
			return nil, fmt.Errorf("email already exists")
		}
		user.Email = req.Email
	}

	if req.Role != "" {
		user.Role = req.Role
	}

	if req.Status != "" {
		user.Status = req.Status
	}

	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}

	// Update user
	_, err = database.DB.ID(id).Update(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	response := user.ToResponse()
	return &response, nil
}

// DeleteUser deletes a user
func (s *UserService) DeleteUser(id int64) error {
	// Check if user exists
	var user models.User
	has, err := database.DB.ID(id).Get(&user)
	if err != nil {
		return fmt.Errorf("failed to get user: %v", err)
	}
	if !has {
		return fmt.Errorf("user not found")
	}

	// Delete user
	_, err = database.DB.ID(id).Delete(&models.User{})
	if err != nil {
		return fmt.Errorf("failed to delete user: %v", err)
	}

	return nil
}

// Login authenticates a user and returns a token
func (s *UserService) Login(req *models.LoginRequest) (*models.LoginResponse, error) {
	// Get user by email
	var user models.User
	has, err := database.DB.Where("email = ?", req.Email).Get(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("invalid email or password")
	}

	// Check if user is active
	if user.Status != "active" {
		return nil, fmt.Errorf("user account is inactive")
	}

	// Check password
	if !middleware.CheckPassword(req.Password, user.Password) {
		return nil, fmt.Errorf("invalid email or password")
	}

	// Generate token
	token, err := middleware.GenerateToken(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %v", err)
	}

	response := &models.LoginResponse{
		User:  user.ToResponse(),
		Token: token,
	}

	return response, nil
}

// GetUserStats returns user statistics
func (s *UserService) GetUserStats() (map[string]interface{}, error) {
	var totalUsers int64
	var activeUsers int64

	// Get total users
	totalUsers, err := database.DB.Count(&models.User{})
	if err != nil {
		return nil, fmt.Errorf("failed to count total users: %v", err)
	}

	// Get active users
	activeUsers, err = database.DB.Where("status = ?", "active").Count(&models.User{})
	if err != nil {
		return nil, fmt.Errorf("failed to count active users: %v", err)
	}

	stats := map[string]interface{}{
		"total_users":  totalUsers,
		"active_users": activeUsers,
	}

	return stats, nil
}
