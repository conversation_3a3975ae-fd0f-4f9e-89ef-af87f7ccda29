package services

import (
	"fmt"

	"xorm.io/xorm"

	"damoncoo/coremovie/database"
	"damoncoo/coremovie/models"
)

type EpisodeService struct {
	db *xorm.Engine
}

func NewEpisodeService() *EpisodeService {
	return &EpisodeService{
		db: database.DB,
	}
}

// CreateEpisode creates a new episode
func (s *EpisodeService) CreateEpisode(episode *models.Episode) error {
	_, err := s.db.Insert(episode)
	return err
}

// GetEpisodesBySeriesID retrieves all episodes for a given series ID
func (s *EpisodeService) GetEpisodesBySeriesID(seriesID int64) ([]models.Episode, error) {
	var episodes []models.Episode
	err := s.db.Where("series_id = ?", seriesID).OrderBy("episode_num").Find(&episodes)
	return episodes, err
}

// GetEpisodeByID retrieves a specific episode by its ID
func (s *EpisodeService) GetEpisodeByID(id int64) (*models.Episode, error) {
	episode := &models.Episode{}
	has, err := s.db.ID(id).Get(episode)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("episode not found")
	}
	return episode, nil
}

// UpdateEpisode updates an existing episode
func (s *EpisodeService) UpdateEpisode(episode *models.Episode) error {
	_, err := s.db.ID(episode.ID).Update(episode)
	return err
}

// DeleteEpisode deletes an episode by its ID
func (s *EpisodeService) DeleteEpisode(id int64) error {
	_, err := s.db.ID(id).Delete(&models.Episode{})
	return err
}

// DeleteEpisodesBySeriesID deletes all episodes for a given series ID
func (s *EpisodeService) DeleteEpisodesBySeriesID(seriesID int64) error {
	_, err := s.db.Where("series_id = ?", seriesID).Delete(&models.Episode{})
	return err
}