package services

import (
	"fmt"

	"damoncoo/coremovie/database"
	"damoncoo/coremovie/models"
)

// CategoryService handles category-related business logic
type CategoryService struct{}

// NewCategoryService creates a new CategoryService instance
func NewCategoryService() *CategoryService {
	return &CategoryService{}
}

// GetAllCategories retrieves all categories with optional pagination
func (s *CategoryService) GetAllCategories(page, limit int, includeStats bool) ([]models.CategoryWithStats, int64, error) {
	var categories []models.Category
	var total int64

	// Get total count
	total, err := database.DB.Count(&models.Category{})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count categories: %v", err)
	}

	// Build query
	query := database.DB.OrderBy("created_at DESC")
	
	// Apply pagination if specified
	if limit > 0 {
		offset := (page - 1) * limit
		query = query.Limit(limit, offset)
	}

	// Get categories
	err = query.Find(&categories)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get categories: %v", err)
	}

	// Convert to response format with stats if requested
	var categoryResponses []models.CategoryWithStats
	for _, category := range categories {
		categoryWithStats := models.CategoryWithStats{
			Category: category,
		}

		if includeStats {
			// Get video count for this category
			videoCount, err := database.DB.Where("category_id = ?", category.ID).Count(&models.Video{})
			if err != nil {
				return nil, 0, fmt.Errorf("failed to count videos for category %d: %v", category.ID, err)
			}
			categoryWithStats.VideoCount = videoCount
		}

		categoryResponses = append(categoryResponses, categoryWithStats)
	}

	return categoryResponses, total, nil
}

// GetCategoryByID retrieves a category by ID
func (s *CategoryService) GetCategoryByID(id int64, includeStats bool) (*models.CategoryWithStats, error) {
	var category models.Category
	has, err := database.DB.ID(id).Get(&category)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("category not found")
	}

	response := &models.CategoryWithStats{
		Category: category,
	}

	if includeStats {
		// Get video count for this category
		videoCount, err := database.DB.Where("category_id = ?", category.ID).Count(&models.Video{})
		if err != nil {
			return nil, fmt.Errorf("failed to count videos for category: %v", err)
		}
		response.VideoCount = videoCount
	}

	return response, nil
}

// CreateCategory creates a new category
func (s *CategoryService) CreateCategory(req *models.CreateCategoryRequest) (*models.Category, error) {
	// Check if category name already exists
	var existingCategory models.Category
	has, err := database.DB.Where("name = ?", req.Name).Get(&existingCategory)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing category: %v", err)
	}
	if has {
		return nil, fmt.Errorf("category name already exists")
	}

	// Set default values
	color := req.Color
	if color == "" {
		color = "#6366f1" // Default indigo color
	}

	icon := req.Icon
	if icon == "" {
		icon = "fas fa-folder" // Default folder icon
	}

	// Create category
	category := &models.Category{
		Name:        req.Name,
		Description: req.Description,
		Color:       color,
		Icon:        icon,
		Status:      "active",
	}

	_, err = database.DB.Insert(category)
	if err != nil {
		return nil, fmt.Errorf("failed to create category: %v", err)
	}

	return category, nil
}

// UpdateCategory updates an existing category
func (s *CategoryService) UpdateCategory(id int64, req *models.UpdateCategoryRequest) (*models.Category, error) {
	// Get existing category
	var category models.Category
	has, err := database.DB.ID(id).Get(&category)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("category not found")
	}

	// Update fields if provided
	if req.Name != "" {
		// Check if name already exists (excluding current category)
		var existingCategory models.Category
		has, err := database.DB.Where("name = ? AND id != ?", req.Name, id).Get(&existingCategory)
		if err != nil {
			return nil, fmt.Errorf("failed to check existing category name: %v", err)
		}
		if has {
			return nil, fmt.Errorf("category name already exists")
		}
		category.Name = req.Name
	}

	if req.Description != "" {
		category.Description = req.Description
	}

	if req.Color != "" {
		category.Color = req.Color
	}

	if req.Icon != "" {
		category.Icon = req.Icon
	}

	if req.Status != "" {
		category.Status = req.Status
	}

	// Update category
	_, err = database.DB.ID(id).Update(&category)
	if err != nil {
		return nil, fmt.Errorf("failed to update category: %v", err)
	}

	return &category, nil
}

// DeleteCategory deletes a category
func (s *CategoryService) DeleteCategory(id int64) error {
	// Check if category exists
	var category models.Category
	has, err := database.DB.ID(id).Get(&category)
	if err != nil {
		return fmt.Errorf("failed to get category: %v", err)
	}
	if !has {
		return fmt.Errorf("category not found")
	}

	// Check if category has videos
	videoCount, err := database.DB.Where("category_id = ?", id).Count(&models.Video{})
	if err != nil {
		return fmt.Errorf("failed to count videos in category: %v", err)
	}
	if videoCount > 0 {
		return fmt.Errorf("cannot delete category with existing videos")
	}

	// Delete category
	_, err = database.DB.ID(id).Delete(&models.Category{})
	if err != nil {
		return fmt.Errorf("failed to delete category: %v", err)
	}

	return nil
}

// GetActiveCategories retrieves all active categories (for dropdowns, etc.)
func (s *CategoryService) GetActiveCategories() ([]models.Category, error) {
	var categories []models.Category
	err := database.DB.Where("status = ?", "active").OrderBy("name ASC").Find(&categories)
	if err != nil {
		return nil, fmt.Errorf("failed to get active categories: %v", err)
	}

	return categories, nil
}

// GetCategoryStats returns category statistics
func (s *CategoryService) GetCategoryStats() (map[string]interface{}, error) {
	var totalCategories int64
	var activeCategories int64

	// Get total categories
	totalCategories, err := database.DB.Count(&models.Category{})
	if err != nil {
		return nil, fmt.Errorf("failed to count total categories: %v", err)
	}

	// Get active categories
	activeCategories, err = database.DB.Where("status = ?", "active").Count(&models.Category{})
	if err != nil {
		return nil, fmt.Errorf("failed to count active categories: %v", err)
	}

	stats := map[string]interface{}{
		"total_categories":  totalCategories,
		"active_categories": activeCategories,
	}

	return stats, nil
}
