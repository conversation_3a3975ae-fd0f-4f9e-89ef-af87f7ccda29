package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID        int64     `json:"id" xorm:"pk autoincr 'id'"`
	Username  string    `json:"username" xorm:"varchar(50) notnull unique 'username'"`
	Email     string    `json:"email" xorm:"varchar(100) notnull unique 'email'"`
	Password  string    `json:"-" xorm:"varchar(255) notnull 'password'"`
	Role      string    `json:"role" xorm:"varchar(20) notnull default('user') 'role'"`
	Avatar    string    `json:"avatar" xorm:"varchar(255) 'avatar'"`
	Status    string    `json:"status" xorm:"varchar(20) notnull default('active') 'status'"`
	CreatedAt time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt time.Time `json:"updated_at" xorm:"updated 'updated_at'"`
}

// UserResponse represents user data for API responses (without password)
type UserResponse struct {
	ID        int64     `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	Avatar    string    `json:"avatar"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateUserRequest represents the request body for creating a user
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Role     string `json:"role" binding:"omitempty,oneof=admin user"`
}

// UpdateUserRequest represents the request body for updating a user
type UpdateUserRequest struct {
	Username string `json:"username" binding:"omitempty,min=3,max=50"`
	Email    string `json:"email" binding:"omitempty,email"`
	Role     string `json:"role" binding:"omitempty,oneof=admin user"`
	Status   string `json:"status" binding:"omitempty,oneof=active inactive"`
	Avatar   string `json:"avatar" binding:"omitempty"`
}

// LoginRequest represents the request body for user login
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents the response for successful login
type LoginResponse struct {
	User  UserResponse `json:"user"`
	Token string       `json:"token"`
}

// TableName returns the table name for User model
func (User) TableName() string {
	return "users"
}

// ToResponse converts User to UserResponse
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:        u.ID,
		Username:  u.Username,
		Email:     u.Email,
		Role:      u.Role,
		Avatar:    u.Avatar,
		Status:    u.Status,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}
