package models

import (
	"time"
)

// Category represents a video category
type Category struct {
	ID          int64     `json:"id" xorm:"pk autoincr 'id'"`
	Name        string    `json:"name" xorm:"varchar(100) notnull unique 'name'"`
	Description string    `json:"description" xorm:"text 'description'"`
	Color       string    `json:"color" xorm:"varchar(7) 'color'"` // Hex color code
	Icon        string    `json:"icon" xorm:"varchar(50) 'icon'"`  // Font Awesome icon class
	Status      string    `json:"status" xorm:"varchar(20) notnull default('active') 'status'"`
	CreatedAt   time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt   time.Time `json:"updated_at" xorm:"updated 'updated_at'"`
}

// CreateCategoryRequest represents the request body for creating a category
type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"omitempty,max=500"`
	Color       string `json:"color" binding:"omitempty,len=7"` // Hex color validation
	Icon        string `json:"icon" binding:"omitempty,max=50"`
}

// UpdateCategoryRequest represents the request body for updating a category
type UpdateCategoryRequest struct {
	Name        string `json:"name" binding:"omitempty,min=2,max=100"`
	Description string `json:"description" binding:"omitempty,max=500"`
	Color       string `json:"color" binding:"omitempty,len=7"`
	Icon        string `json:"icon" binding:"omitempty,max=50"`
	Status      string `json:"status" binding:"omitempty,oneof=active inactive"`
}

// CategoryWithStats represents a category with video count statistics
type CategoryWithStats struct {
	Category
	VideoCount int64 `json:"video_count"`
}

// TableName returns the table name for Category model
func (Category) TableName() string {
	return "categories"
}
