package models

import (
	"fmt"
	"time"
)

// Video represents a video in the system
// Videos can also function as series that contain episodes
type Video struct {
	ID          int64     `json:"id" xorm:"pk autoincr 'id'"`
	Title       string    `json:"title" xorm:"varchar(255) notnull 'title'"`
	Description string    `json:"description" xorm:"text 'description'"`
	Filename    string    `json:"filename" xorm:"varchar(255) notnull 'filename'"`
	FilePath    string    `json:"file_path" xorm:"varchar(500) notnull 'file_path'"`
	Thumbnail   string    `json:"thumbnail" xorm:"varchar(500) 'thumbnail'"`
	Duration    int       `json:"duration" xorm:"int 'duration'"`      // Duration in seconds
	FileSize    int64     `json:"file_size" xorm:"bigint 'file_size'"` // File size in bytes
	MimeType    string    `json:"mime_type" xorm:"varchar(100) 'mime_type'"`
	CategoryID  int64     `json:"category_id" xorm:"bigint 'category_id'"`
	UserID      int64     `json:"user_id" xorm:"bigint 'user_id'"`
	Views       int64     `json:"views" xorm:"bigint default(0) 'views'"`
	Status      string    `json:"status" xorm:"varchar(20) notnull default('processing') 'status'"`
	CreatedAt   time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt   time.Time `json:"updated_at" xorm:"updated 'updated_at'"`
	
	// Videos can function as series
	IsSeries    bool      `json:"is_series" xorm:"bool default(false) 'is_series'"` // Indicates if this video is a series
}

// VideoWithDetails represents a video with related category, user, and episode information
type VideoWithDetails struct {
	Video
	Category  *Category     `json:"category,omitempty"`
	User      *UserResponse `json:"user,omitempty"`
	URL       string        `json:"url,omitempty"` // URL to access the video file
	Episodes  []Episode     `json:"episodes,omitempty"` // Associated episodes if this is a series
}

// CreateVideoRequest represents the request body for creating a video
type CreateVideoRequest struct {
	Title       string `json:"title" binding:"required,min=2,max=255"`
	Description string `json:"description" binding:"omitempty,max=1000"`
	CategoryID  int64  `json:"category_id" binding:"required,min=1"`
	IsSeries    bool   `json:"is_series"`
}

// UploadVideoRequest represents the request body for uploading a video
type UploadVideoRequest struct {
	VideoID int64 `query:"video_id" binding:"required"`
}

// UpdateVideoRequest represents the request body for updating a video
type UpdateVideoRequest struct {
	Title       string `json:"title" binding:"omitempty,min=2,max=255"`
	Description string `json:"description" binding:"omitempty,max=1000"`
	CategoryID  int64  `json:"category_id" binding:"omitempty,min=1"`
	Status      string `json:"status" binding:"omitempty,oneof=processing published private"`
	IsSeries    bool   `json:"is_series"`
}

// PresignedURLRequest represents a request for S3 presigned URL
type PresignedURLRequest struct {
	VideoID  int64  `json:"video_id" binding:"required"`
	Filename string `json:"filename" binding:"required"`
	FileSize int64  `json:"file_size" binding:"required"`
	FileType string `json:"file_type" binding:"required"`
}

// PresignedURLResponse represents a response with S3 presigned URL
type PresignedURLResponse struct {
	URL      string `json:"url"`
	FilePath string `json:"file_path"`
}

// VideoStats represents video statistics for dashboard
type VideoStats struct {
	TotalVideos      int64 `json:"total_videos"`
	PublishedVideos  int64 `json:"published_videos"`
	ProcessingVideos int64 `json:"processing_videos"`
	TotalViews       int64 `json:"total_views"`
	TotalStorage     int64 `json:"total_storage"` // Total storage used in bytes
}

// TableName returns the table name for Video model
func (Video) TableName() string {
	return "videos"
}

// FormatFileSize returns human-readable file size
func (v *Video) FormatFileSize() string {
	const unit = 1024
	if v.FileSize < unit {
		return "< 1 KB"
	}
	div, exp := int64(unit), 0
	for n := v.FileSize / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(v.FileSize)/float64(div), "KMGTPE"[exp])
}

// FormatDuration returns human-readable duration
func (v *Video) FormatDuration() string {
	if v.Duration < 60 {
		return fmt.Sprintf("%ds", v.Duration)
	}
	minutes := v.Duration / 60
	seconds := v.Duration % 60
	if minutes < 60 {
		return fmt.Sprintf("%dm %ds", minutes, seconds)
	}
	hours := minutes / 60
	minutes = minutes % 60
	return fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds)
}