# CoreMovie Code Index

This document provides an index of the CoreMovie application codebase, detailing the structure, components, and functionality of the system.

## Project Overview

CoreMovie is a video management system built with:
- Backend: Go with Gin framework and XORM ORM
- Frontend: React with Redux
- Database: MySQL

## Directory Structure

```
.
├── database/          # Database initialization and connection
├── frontend/          # React frontend application
├── middleware/        # Authentication and other middleware
├── models/            # Data models and structures
├── routes/            # API route definitions
├── services/          # Business logic implementations
├── main.go            # Application entry point
└── README.md          # Project documentation
```

## Core Components

### 1. Models

#### User Model (`models/user.go`)
- Represents system users with fields for ID, username, email, password, role, etc.
- Includes supporting structures:
  - `UserResponse`: User data for API responses (without password)
  - `CreateUserRequest`: Request body for creating a user
  - `UpdateUserRequest`: Request body for updating a user
  - `LoginRequest`: Request body for user login
  - `LoginResponse`: Response for successful login

#### Category Model (`models/category.go`)
- Represents video categories with fields for ID, name, description, color, icon, etc.
- Includes supporting structures:
  - `CreateCategoryRequest`: Request body for creating a category
  - `UpdateCategoryRequest`: Request body for updating a category
  - `CategoryWithStats`: Category with video count statistics

#### Video Model (`models/video.go`)
- Represents videos in the system with fields for ID, title, description, file information, duration, etc.
- Includes supporting structures:
  - `VideoWithDetails`: Video with related category and user information
  - `CreateVideoRequest`: Request body for creating a video
  - `UpdateVideoRequest`: Request body for updating a video
  - `VideoUploadResponse`: Response after video upload
  - `VideoStats`: Video statistics for dashboard

### 2. Database (`database/database.go`)
- Initializes MySQL database connection using XORM
- Handles database migrations (schema synchronization)
- Seeds initial data (creates default admin user if none exists)
- Manages connection pool settings

### 3. Middleware (`middleware/auth.go`)
- Implements authentication middleware
- Provides password hashing functionality
- Implements admin role checking middleware

### 4. Services

#### User Service (`services/user_service.go`)
- Handles user-related business logic
- Functions include:
  - `GetAllUsers`: Retrieves all users with pagination
  - `GetUserByID`: Retrieves a user by ID
  - `CreateUser`: Creates a new user
  - `UpdateUser`: Updates an existing user
  - `DeleteUser`: Deletes a user
  - `Login`: Authenticates a user and generates JWT token
  - `GetUserStats`: Retrieves user statistics

#### Category Service (`services/category_service.go`)
- Handles category-related business logic
- Functions include:
  - `GetAllCategories`: Retrieves all categories with optional pagination
  - `GetActiveCategories`: Retrieves all active categories
  - `GetCategoryByID`: Retrieves a category by ID
  - `CreateCategory`: Creates a new category
  - `UpdateCategory`: Updates an existing category
  - `DeleteCategory`: Deletes a category
  - `GetCategoryStats`: Retrieves category statistics

#### Video Service (`services/video_service.go`)
- Handles video-related business logic
- Functions include:
  - `GetAllVideos`: Retrieves all videos with pagination and filtering
  - `GetVideoByID`: Retrieves a video by ID with details
  - `CreateVideo`: Creates a new video entry
  - `UpdateVideo`: Updates an existing video
  - `DeleteVideo`: Deletes a video
  - `UploadVideo`: Handles video file upload
  - `GetVideoStats`: Retrieves video statistics
  - `IncrementViews`: Increments video view count

### 5. Routes

#### User Routes (`routes/user_routes.go`)
- Defines API endpoints for user management
- Public endpoints:
  - `POST /api/v1/auth/login`: User authentication
- Protected endpoints:
  - `GET /api/v1/users`: Get all users (with pagination)
  - `GET /api/v1/users/:id`: Get specific user
  - `PUT /api/v1/users/:id`: Update user
  - `DELETE /api/v1/users/:id`: Delete user
  - `GET /api/v1/users/stats`: Get user statistics
- Admin endpoints:
  - `POST /api/v1/admin/users`: Create new user

#### Category Routes (`routes/category_routes.go`)
- Defines API endpoints for category management
- Public endpoints:
  - `GET /api/v1/categories`: Get all categories
  - `GET /api/v1/categories/active`: Get active categories
  - `GET /api/v1/categories/:id`: Get specific category
  - `GET /api/v1/categories/stats`: Get category statistics
- Admin endpoints:
  - `POST /api/v1/admin/categories`: Create new category
  - `PUT /api/v1/admin/categories/:id`: Update category
  - `DELETE /api/v1/admin/categories/:id`: Delete category

#### Video Routes (`routes/video_routes.go`)
- Defines API endpoints for video management
- Public endpoints:
  - `GET /api/v1/videos`: Get all videos (with filtering)
  - `GET /api/v1/videos/:id`: Get specific video
  - `POST /api/v1/videos/:id/view`: Increment view count
  - `GET /api/v1/videos/stats`: Get video statistics
- Protected endpoints:
  - `POST /api/v1/videos`: Create new video entry
  - `POST /api/v1/videos/:id/upload`: Upload video file
  - `PUT /api/v1/videos/:id`: Update video
  - `DELETE /api/v1/videos/:id`: Delete video
- Static file serving:
  - `/uploads`: Serves uploaded video files

### 6. Main Application (`main.go`)
- Entry point of the application
- Initializes environment variables
- Sets up database connection
- Runs database migrations
- Seeds initial data
- Configures Gin router with CORS
- Registers all routes
- Starts the HTTP server

## Frontend Structure

The frontend is a React application with the following key components:

### API Layer (`frontend/src/api/`)
- Client configuration for API requests
- Service modules for each entity (auth, categories, users, videos)

### Components (`frontend/src/components/`)
- Auth: Login component
- Categories: Category management and modal components
- Common: Pagination component
- Dashboard: Dashboard, recent videos, and stats card components
- Layout: Header, layout, and sidebar components
- Settings: Settings component
- Users: User management component
- Videos: Various video-related components (card, grid, filters, player, etc.)

### State Management (`frontend/src/store/`)
- Redux slices for auth, categories, UI, and videos
- Centralized state management

### Types (`frontend/src/types/`)
- TypeScript type definitions for the frontend

## Environment Variables

The application uses the following environment variables (defined in `.env`):
- `DB_HOST`: Database host
- `DB_PORT`: Database port
- `DB_USER`: Database user
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name
- `JWT_SECRET`: Secret key for JWT token generation
- `GIN_MODE`: Gin framework mode (debug/release)
- `PORT`: Server port

## API Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | /api/v1/auth/login | User login | No |
| GET | /api/v1/users | Get all users | Yes |
| GET | /api/v1/users/:id | Get user by ID | Yes |
| PUT | /api/v1/users/:id | Update user | Yes |
| DELETE | /api/v1/users/:id | Delete user | Yes |
| GET | /api/v1/users/stats | Get user statistics | Yes |
| POST | /api/v1/admin/users | Create user | Admin |
| GET | /api/v1/categories | Get all categories | No |
| GET | /api/v1/categories/active | Get active categories | No |
| GET | /api/v1/categories/:id | Get category by ID | No |
| GET | /api/v1/categories/stats | Get category statistics | No |
| POST | /api/v1/admin/categories | Create category | Admin |
| PUT | /api/v1/admin/categories/:id | Update category | Admin |
| DELETE | /api/v1/admin/categories/:id | Delete category | Admin |
| GET | /api/v1/videos | Get all videos | No |
| GET | /api/v1/videos/:id | Get video by ID | No |
| POST | /api/v1/videos/:id/view | Increment view count | No |
| GET | /api/v1/videos/stats | Get video statistics | No |
| POST | /api/v1/videos | Create video entry | Yes |
| POST | /api/v1/videos/:id/upload | Upload video file | Yes |
| PUT | /api/v1/videos/:id | Update video | Yes |
| DELETE | /api/v1/videos/:id | Delete video | Yes |
| GET | /uploads/* | Serve video files | No |

This index provides a comprehensive overview of the CoreMovie application's structure and functionality.