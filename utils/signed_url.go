package utils

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/url"
	"os"
	"strconv"
	"time"
)

// SignedURLConfig holds configuration for signed URL generation
type SignedURLConfig struct {
	Secret    string        // Secret key for signing
	ExpiresIn time.Duration // How long the URL is valid for
	BaseURL   string        // Base URL of the server
}

// SignedURLParams represents parameters for a signed URL
type SignedURLParams struct {
	VideoID   int64  `json:"video_id"`
	Filename  string `json:"filename"`
	FileSize  int64  `json:"file_size"`
	FileType  string `json:"file_type"`
	FilePath  string `json:"file_path"`
	ExpiresAt int64  `json:"expires_at"`
}

// NewSignedURLConfig creates a new signed URL configuration
func NewSignedURLConfig() *SignedURLConfig {
	secret := os.Getenv("SIGNED_URL_SECRET")
	if secret == "" {
		secret = os.Getenv("JWT_SECRET") // Fallback to JWT secret
		if secret == "" {
			panic("SIGNED_URL_SECRET is not set")
		}
	}

	baseURL := os.Getenv("LOCAL_BASE_URL")
	if baseURL == "" {
		panic("LOCAL_BASE_URL is not set")
	}

	return &SignedURLConfig{
		Secret:    secret,
		ExpiresIn: 15 * time.Minute, // URLs expire in 15 minutes
		BaseURL:   baseURL,
	}
}

// GenerateSignedURL creates a signed URL for file upload
func (c *SignedURLConfig) GenerateSignedURL(params *SignedURLParams) (string, error) {
	// Set expiration time
	params.ExpiresAt = time.Now().Add(c.ExpiresIn).Unix()

	// Create the base URL
	uploadURL := fmt.Sprintf("%s/api/v1/videos/%d/signed-upload", c.BaseURL, params.VideoID)

	// Create URL with query parameters
	u, err := url.Parse(uploadURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %v", err)
	}

	// Add query parameters
	q := u.Query()
	q.Set("video_id", strconv.FormatInt(params.VideoID, 10))
	q.Set("filename", params.Filename)
	q.Set("file_size", strconv.FormatInt(params.FileSize, 10))
	q.Set("file_type", params.FileType)
	q.Set("file_path", params.FilePath)
	q.Set("expires_at", strconv.FormatInt(params.ExpiresAt, 10))

	// Generate signature
	signature := c.generateSignature(params)
	q.Set("signature", signature)

	u.RawQuery = q.Encode()
	return u.String(), nil
}

// ValidateSignedURL validates a signed URL and extracts parameters
func (c *SignedURLConfig) ValidateSignedURL(queryParams url.Values) (*SignedURLParams, error) {
	// Extract parameters
	videoIDStr := queryParams.Get("video_id")
	filename := queryParams.Get("filename")
	fileSizeStr := queryParams.Get("file_size")
	fileType := queryParams.Get("file_type")
	filePath := queryParams.Get("file_path")
	expiresAtStr := queryParams.Get("expires_at")
	providedSignature := queryParams.Get("signature")

	if videoIDStr == "" || filename == "" || fileSizeStr == "" || fileType == "" ||
		filePath == "" || expiresAtStr == "" || providedSignature == "" {
		return nil, fmt.Errorf("missing required parameters")
	}

	// Parse numeric values
	videoID, err := strconv.ParseInt(videoIDStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid video_id: %v", err)
	}

	fileSize, err := strconv.ParseInt(fileSizeStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid file_size: %v", err)
	}

	expiresAt, err := strconv.ParseInt(expiresAtStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid expires_at: %v", err)
	}

	params := &SignedURLParams{
		VideoID:   videoID,
		Filename:  filename,
		FileSize:  fileSize,
		FileType:  fileType,
		FilePath:  filePath,
		ExpiresAt: expiresAt,
	}

	// Check if URL has expired
	if time.Now().Unix() > expiresAt {
		return nil, fmt.Errorf("signed URL has expired")
	}

	// Validate signature
	expectedSignature := c.generateSignature(params)
	if !hmac.Equal([]byte(providedSignature), []byte(expectedSignature)) {
		return nil, fmt.Errorf("invalid signature")
	}

	return params, nil
}

// generateSignature creates an HMAC signature for the given parameters
func (c *SignedURLConfig) generateSignature(params *SignedURLParams) string {
	// Create the string to sign
	stringToSign := fmt.Sprintf("%d|%s|%d|%s|%s|%d",
		params.VideoID,
		params.Filename,
		params.FileSize,
		params.FileType,
		params.FilePath,
		params.ExpiresAt,
	)

	// Create HMAC signature
	h := hmac.New(sha256.New, []byte(c.Secret))
	h.Write([]byte(stringToSign))
	return hex.EncodeToString(h.Sum(nil))
}
