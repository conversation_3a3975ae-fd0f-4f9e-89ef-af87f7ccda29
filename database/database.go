package database

import (
	"fmt"
	"log"
	"os"

	_ "github.com/go-sql-driver/mysql"
	"xorm.io/xorm"

	"damoncoo/coremovie/models"
)

var DB *xorm.Engine

// Config holds database configuration
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	Database string
}

// Initialize sets up the database connection
func Initialize() error {
	config := Config{
		Host:     getEnv("DB_HOST", "localhost"),
		Port:     getEnv("DB_PORT", "3306"),
		User:     getEnv("DB_USER", "root"),
		Password: getEnv("DB_PASSWORD", ""),
		Database: getEnv("DB_NAME", "coremovie"),
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.User,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
	)

	var err error
	DB, err = xorm.NewEngine("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// Test the connection
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// Set connection pool settings
	DB.SetMaxIdleConns(10)
	DB.SetMaxOpenConns(100)

	// Enable logging in development
	if getEnv("GIN_MODE", "debug") == "debug" {
		DB.ShowSQL(true)
	}

	log.Println("Database connected successfully")
	return nil
}

// Migrate creates or updates database tables
func Migrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// Sync database schema
	err := DB.Sync2(
		new(models.User),
		new(models.Category),
		new(models.Video),
		new(models.Episode),
	)
	if err != nil {
		return fmt.Errorf("failed to migrate database: %v", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// SeedData inserts initial data into the database
func SeedData() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// Check if admin user exists
	var userCount int64
	userCount, err := DB.Count(&models.User{})
	if err != nil {
		return fmt.Errorf("failed to count users: %v", err)
	}

	// Create default admin user if no users exist
	if userCount == 0 {
		adminUser := &models.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password: password
			Role:     "admin",
			Status:   "active",
		}

		_, err = DB.Insert(adminUser)
		if err != nil {
			return fmt.Errorf("failed to create admin user: %v", err)
		}
		log.Println("Default admin user created (email: <EMAIL>, password: password)")
	}

	// Check if categories exist
	var categoryCount int64
	categoryCount, err = DB.Count(&models.Category{})
	if err != nil {
		return fmt.Errorf("failed to count categories: %v", err)
	}

	// Create default categories if none exist
	if categoryCount == 0 {
		defaultCategories := []*models.Category{
			{
				Name:        "Technology",
				Description: "Technology and programming related videos",
				Color:       "#a78bfa",
				Icon:        "fas fa-laptop-code",
				Status:      "active",
			},
			{
				Name:        "Travel",
				Description: "Travel and adventure videos",
				Color:       "#34d399",
				Icon:        "fas fa-plane",
				Status:      "active",
			},
			{
				Name:        "Cooking",
				Description: "Cooking and recipe videos",
				Color:       "#f87171",
				Icon:        "fas fa-utensils",
				Status:      "active",
			},
		}

		for _, category := range defaultCategories {
			_, err = DB.Insert(category)
			if err != nil {
				return fmt.Errorf("failed to create category %s: %v", category.Name, err)
			}
		}
		log.Println("Default categories created")
	}

	return nil
}

// Close closes the database connection
func Close() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// getEnv gets environment variable with fallback
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
