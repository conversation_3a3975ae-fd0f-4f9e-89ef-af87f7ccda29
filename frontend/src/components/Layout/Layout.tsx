import React, { useEffect } from 'react';
import { useAppDispatch } from '../../store';
import { initializeAuth } from '../../store/authSlice';
import Sidebar from './Sidebar';
import Header from './Header';
import { Outlet } from 'react-router-dom';

const Layout: React.FC = () => {
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  return (
    <div className="flex h-screen bg-gray-200">
      <Sidebar />
      
      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-y-auto">
        <Header />
        
        <div className="p-4 md:p-8">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default Layout;
