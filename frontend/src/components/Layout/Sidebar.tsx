import React from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import { setSidebarOpen } from '../../store/uiSlice';
import { logout } from '../../store/authSlice';
import { Link, useLocation, useNavigate } from 'react-router-dom';

const Sidebar: React.FC = () => {
  const dispatch = useAppDispatch();
  const { sidebarOpen } = useAppSelector((state) => state.ui);
  const { user } = useAppSelector((state) => state.auth);
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt', path: '/dashboard' },
    { id: 'videos', label: 'Videos', icon: 'fas fa-video', path: '/videos' },
    { id: 'users', label: 'Users', icon: 'fas fa-users', path: '/users' },
    { id: 'categories', label: 'Categories', icon: 'fas fa-tags', path: '/categories' },
    { id: 'settings', label: 'Settings', icon: 'fas fa-cog', path: '/settings' },
  ];

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  // Determine current section based on the current path
  const getCurrentSection = () => {
    const path = location.pathname;
    const item = menuItems.find(item => path.includes(item.path.replace('/', '')));
    return item ? item.id : 'dashboard';
  };

  const activeSection = getCurrentSection();

  return (
    <>
      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black bg-opacity-50 md:hidden"
          onClick={() => dispatch(setSidebarOpen(false))}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-30 w-64 px-4 py-7 overflow-y-auto text-white transition-transform duration-300 ease-in-out transform bg-gray-800 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } md:relative md:translate-x-0`}
      >
        {/* Logo */}
        <div className="flex items-center justify-between px-2">
          <Link to="/dashboard" className="flex items-center space-x-2 text-2xl font-bold">
            <i className="fas fa-play-circle text-indigo-400"></i>
            <span>Vortex</span>
          </Link>
          {/* Close button for mobile */}
          <button
            className="text-gray-400 md:hidden hover:text-white"
            onClick={() => dispatch(setSidebarOpen(false))}
          >
            <i className="fas fa-times text-xl"></i>
          </button>
        </div>

        {/* Navigation Links */}
        <nav className="mt-10">
          {menuItems.map((item) => (
            <Link
              key={item.id}
              to={item.path}
              className={`w-full flex items-center px-4 py-3 mt-4 text-left transition-colors duration-200 rounded-lg hover:bg-gray-700 hover:text-white ${
                activeSection === item.id
                  ? 'bg-indigo-500 text-white'
                  : 'text-gray-300'
              }`}
              onClick={() => {
                // Close sidebar on mobile after selection
                if (window.innerWidth < 768) {
                  dispatch(setSidebarOpen(false));
                }
              }}
            >
              <i className={`${item.icon} w-5 h-5`}></i>
              <span className="mx-4 font-medium">{item.label}</span>
            </Link>
          ))}
        </nav>

        {/* User Profile */}
        <div className="absolute bottom-0 left-0 w-full p-4 border-t border-gray-700">
          <div className="flex items-center">
            <img
              className="w-10 h-10 rounded-full"
              src={user?.avatar || `https://ui-avatars.com/api/?name=${user?.username}&background=7e22ce&color=ffffff`}
              alt="User Avatar"
            />
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-white">{user?.username}</p>
              <button
                onClick={handleLogout}
                className="text-xs text-indigo-400 hover:underline"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;