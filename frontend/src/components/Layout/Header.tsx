import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { setSidebarOpen } from '../../store/uiSlice';

const Header: React.FC = () => {
  const dispatch = useDispatch();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement search functionality
    console.log('Search query:', searchQuery);
  };

  return (
    <div className="flex items-center justify-between h-16 bg-white border-b border-gray-200 px-4 md:px-8">
      {/* Mobile Menu Button */}
      <button
        className="text-gray-500 md:hidden focus:outline-none"
        onClick={() => dispatch(setSidebarOpen(true))}
      >
        <i className="fas fa-bars text-xl"></i>
      </button>

      {/* Search Bar */}
      <form onSubmit={handleSearch} className="relative w-full max-w-xs ml-auto">
        <span className="absolute inset-y-0 left-0 flex items-center pl-3">
          <i className="fas fa-search text-gray-400"></i>
        </span>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full py-2 pl-10 pr-4 text-gray-700 bg-gray-100 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder="Search..."
        />
      </form>
    </div>
  );
};

export default Header;
