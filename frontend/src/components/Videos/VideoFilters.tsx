import React from 'react';
import { Category } from '../../types';

interface VideoFiltersProps {
  categories: Category[];
  filters: {
    category_id: number;
    status: string;
  };
  onFilterChange: (filters: Partial<{ category_id: number; status: string }>) => void;
}

const VideoFilters: React.FC<VideoFiltersProps> = ({ categories, filters, onFilterChange }) => {
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'published', label: 'Published' },
    { value: 'processing', label: 'Processing' },
    { value: 'private', label: 'Private' },
  ];

  return (
    <div className="mb-6">
      <div className="flex flex-wrap gap-4">
        {/* Category Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            value={filters.category_id}
            onChange={(e) => onFilterChange({ category_id: parseInt(e.target.value) })}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            <option value={0}>All Categories</option>
            {categories && categories.length > 0 && categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => onFilterChange({ status: e.target.value })}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Clear Filters */}
        {(filters.category_id !== 0 || filters.status !== '') && (
          <div className="flex items-end">
            <button
              onClick={() => onFilterChange({ category_id: 0, status: '' })}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 underline"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoFilters;