import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideos } from '../../store/videosSlice';
import { fetchActiveCategories } from '../../store/categoriesSlice';
import VideoGrid from './VideoGrid';
import VideoFilters from './VideoFilters';
import UploadVideoModal from './UploadVideoModal';

const VideoManagement: React.FC = () => {
  const dispatch = useAppDispatch();
  const { videos, loading, pagination } = useAppSelector((state) => state.videos);
  const { activeCategories } = useAppSelector((state) => state.categories);
  
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [filters, setFilters] = useState({
    category_id: 0,
    status: '',
    page: 1,
    limit: 12,
  });

  useEffect(() => {
    dispatch(fetchActiveCategories());
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchVideos(filters));
  }, [dispatch, filters]);

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  return (
    <div>
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Video Management</h1>
        <button
          onClick={() => setShowUploadModal(true)}
          className="flex items-center px-4 py-2 font-medium text-white bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all"
        >
          <i className="fas fa-upload mr-2"></i>
          Upload Video
        </button>
      </div>

      {/* Filters */}
      <VideoFilters
        categories={activeCategories}
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      {/* Video Grid */}
      <VideoGrid
        videos={videos}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadVideoModal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          categories={activeCategories}
        />
      )}
    </div>
  );
};

export default VideoManagement;
