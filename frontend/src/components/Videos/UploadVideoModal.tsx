import React, { useState } from "react";
import { useAppDispatch } from "../../store";
import { Category } from "../../types";
import { createVideo, uploadVideo } from "../../store/videosSlice";
import { videosApi, PresignedURLRequest } from "../../api/videos";

interface UploadVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: Category[];
}

const UploadVideoModal: React.FC<UploadVideoModalProps> = ({
  isOpen,
  onClose,
  categories,
}) => {
  const dispatch = useAppDispatch();
  const [step, setStep] = useState(1); // 1: Video info, 2: File upload
  const [videoData, setVideoData] = useState({
    title: "",
    description: "",
    category_id: "",
  });
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [createdVideoId, setCreatedVideoId] = useState<number | null>(null);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setVideoData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const validTypes = [
        "video/mp4",
        "video/avi",
        "video/mov",
        "video/wmv",
        "video/webm",
      ];
      if (!validTypes.includes(selectedFile.type)) {
        alert("Please select a valid video file (MP4, AVI, MOV, WMV, WebM)");
        return;
      }

      // Validate file size (500MB limit)
      const maxSize = 500 * 1024 * 1024; // 500MB
      if (selectedFile.size > maxSize) {
        alert("File size must be less than 500MB");
        return;
      }

      setFile(selectedFile);
    }
  };

  const handleCreateVideo = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!videoData.title || !videoData.category_id) {
      alert("Please fill in all required fields");
      return;
    }

    try {
      const result = await dispatch(
        createVideo({
          title: videoData.title,
          description: videoData.description,
          category_id: parseInt(videoData.category_id),
        })
      ).unwrap();

      setCreatedVideoId(result.id);
      setStep(2);
    } catch (error) {
      alert("Failed to create video: " + error);
    }
  };

  const handleDirectUpload = async () => {
    if (!file || !createdVideoId) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      // Get presigned URL
      const presignedData: PresignedURLRequest = {
        video_id: createdVideoId,
        filename: file.name,
        file_size: file.size,
        file_type: file.type,
      };

      const response = await videosApi.generatePresignedURL(presignedData);

      // Upload file directly to S3
      if (response.url) {
        // Create a custom upload progress tracker
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 10;
          if (progress >= 90) {
            clearInterval(progressInterval);
            progress = 90; // Cap at 90% until upload completes
          }
          setUploadProgress(progress);
        }, 200);

        console.log("Uploading to S3...");
        console.log(response.url);

        // Perform direct upload to S3
        const uploadResponse = await fetch(response.url, {
          method: "PUT",
          headers: {
            "Content-Type": file.type,
          },
          body: file,
        });

        clearInterval(progressInterval);

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed with status ${uploadResponse.status}`);
        }

        setUploadProgress(100);
      } else {
        // Fallback to server-side upload for local storage
        await dispatch(uploadVideo({ id: createdVideoId, file })).unwrap();
        setUploadProgress(100);
      }

      setTimeout(() => {
        onClose();
        resetForm();
      }, 1000);
    } catch (error: any) {
      console.error("Upload error:", error);
      alert("Failed to upload video: " + (error.message || "Unknown error"));
      setUploading(false);
    }
  };

  const resetForm = () => {
    setStep(1);
    setVideoData({ title: "", description: "", category_id: "" });
    setFile(null);
    setUploading(false);
    setUploadProgress(0);
    setCreatedVideoId(null);
  };

  const handleClose = () => {
    if (!uploading) {
      onClose();
      resetForm();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg mx-4 overflow-hidden">
        {/* Modal header with progress indicator */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">
              {step === 1 ? "Create Video" : "Upload Video"}
            </h2>
            <button
              onClick={handleClose}
              disabled={uploading}
              className="text-white hover:text-gray-200 disabled:opacity-50 transition-colors"
            >
              <i className="fas fa-times text-2xl"></i>
            </button>
          </div>
          
          {/* Progress indicator */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-indigo-100 mb-1">
              <span>Step {step} of 2</span>
              <span>{step === 1 ? 'Video details' : 'File upload'}</span>
            </div>
            <div className="w-full bg-indigo-400 rounded-full h-2">
              <div
                className="bg-white h-2 rounded-full transition-all duration-300"
                style={{ width: `${step === 1 ? '50%' : '100%'}` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {step === 1 ? (
            <form onSubmit={handleCreateVideo}>
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={videoData.title}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  placeholder="Enter video title"
                  required
                />
              </div>

              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  name="category_id"
                  value={videoData.category_id}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all appearance-none bg-white bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwb2x5bGluZSBwb2ludHM9IjYgOSAxMiAxNSAxOCA5Ij48L3BvbHlsaW5lPjwvc3ZnPg==')] bg-no-repeat bg-right-4"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="mb-8">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  name="description"
                  value={videoData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  placeholder="Enter video description"
                />
              </div>

              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={handleClose}
                  className="px-6 py-3 text-gray-600 font-medium rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all shadow-md hover:shadow-lg"
                >
                  Next Step
                  <i className="fas fa-arrow-right ml-2"></i>
                </button>
              </div>
            </form>
          ) : (
            <div>
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Select Video File *
                </label>
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                    {file ? (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <i className="fas fa-file-video text-4xl text-indigo-500 mb-3"></i>
                        <p className="text-sm font-medium text-gray-700 text-center px-4">
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {(file.size / (1024 * 1024)).toFixed(2)} MB
                        </p>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-3"></i>
                        <p className="text-sm text-gray-500">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          MP4, AVI, MOV, WMV, WebM (Max: 500MB)
                        </p>
                      </div>
                    )}
                    <input
                      type="file"
                      accept="video/*"
                      onChange={handleFileChange}
                      disabled={uploading}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>

              {uploading && (
                <div className="mb-6">
                  <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
                    <span>Uploading...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-indigo-500 to-purple-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Please do not close this window while uploading
                  </p>
                </div>
              )}

              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => setStep(1)}
                  disabled={uploading}
                  className="px-6 py-3 text-gray-600 font-medium rounded-lg hover:bg-gray-100 disabled:opacity-50 transition-colors"
                >
                  <i className="fas fa-arrow-left mr-2"></i>
                  Back
                </button>
                <button
                  type="button"
                  onClick={handleDirectUpload}
                  disabled={!file || uploading}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-md hover:shadow-lg"
                >
                  {uploading ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Uploading...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-upload mr-2"></i>
                      Upload Video
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadVideoModal;