import React, { useState, useCallback } from 'react';
import { VideoWithDetails } from '../../types';
import VideoCard from './VideoCard';
import VideoPlayerModal from './VideoPlayerModal';
import Pagination from '../Common/Pagination';

interface VideoGridProps {
  videos: VideoWithDetails[];
  loading: boolean;
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    limit: number;
  } | null;
  onPageChange: (page: number) => void;
}

const VideoGrid: React.FC<VideoGridProps> = ({ videos, loading, pagination, onPageChange }) => {
  const [selectedVideo, setSelectedVideo] = useState<VideoWithDetails | null>(null);

  const handleVideoPlay = useCallback((video: VideoWithDetails) => {
    // Ensure we're setting a valid video object
    if (video && video.id) {
      setSelectedVideo(video);
    }
  }, []);

  const handleClosePlayer = useCallback(() => {
    setSelectedVideo(null);
  }, []);

  // Ensure videos is always an array
  const safeVideos = Array.isArray(videos) ? videos : [];

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
            <div className="w-full h-40 bg-gray-300"></div>
            <div className="p-4">
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-2/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (safeVideos.length === 0) {
    return (
      <div className="text-center py-12">
        <i className="fas fa-video text-6xl text-gray-300 mb-4"></i>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No videos found</h3>
        <p className="text-gray-500">Upload your first video to get started.</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {safeVideos.map((video) => (
          <VideoCard
            key={video.id}
            video={video}
            onPlay={handleVideoPlay}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.total_pages > 1 && (
        <div className="mt-8">
          <Pagination
            currentPage={pagination.current_page}
            totalPages={pagination.total_pages}
            onPageChange={onPageChange}
          />
        </div>
      )}

      {/* Video Player Modal */}
      {selectedVideo && selectedVideo.id && (
        <VideoPlayerModal
          video={selectedVideo}
          isOpen={!!selectedVideo}
          onClose={handleClosePlayer}
        />
      )}
    </>
  );
};

export default VideoGrid;