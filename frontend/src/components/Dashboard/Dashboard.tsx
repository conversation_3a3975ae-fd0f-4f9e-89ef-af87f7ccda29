import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideoStats } from '../../store/videosSlice';
import { fetchVideos } from '../../store/videosSlice';
import StatsCard from './StatsCard';
import RecentVideos from './RecentVideos';

const Dashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const { stats } = useAppSelector((state) => state.videos);
  const { loading } = useAppSelector((state) => state.videos);

  useEffect(() => {
    dispatch(fetchVideoStats());
    dispatch(fetchVideos({ limit: 5 })); // Fetch recent videos
  }, [dispatch]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Dashboard</h1>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 mb-8 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Videos"
          value={stats ? formatNumber(stats.total_videos) : '0'}
          icon="fas fa-video"
          color="indigo"
        />
        <StatsCard
          title="Total Users"
          value="84" // This would come from user stats
          icon="fas fa-users"
          color="green"
        />
        <StatsCard
          title="Storage Used"
          value={stats ? formatFileSize(stats.total_storage) : '0 GB'}
          icon="fas fa-hdd"
          color="yellow"
        />
        <StatsCard
          title="Total Views"
          value={stats ? formatNumber(stats.total_views) : '0'}
          icon="fas fa-chart-bar"
          color="red"
        />
      </div>

      {/* Recent Videos */}
      <RecentVideos />
    </div>
  );
};

export default Dashboard;
