import React from 'react';

interface StatsCardProps {
  title: string;
  value: string;
  icon: string;
  color: 'indigo' | 'green' | 'yellow' | 'red';
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, color }) => {
  const colorClasses = {
    indigo: 'bg-indigo-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500 uppercase">{title}</p>
          <p className="text-3xl font-bold text-gray-800">{value}</p>
        </div>
        <div className={`p-3 text-white rounded-full ${colorClasses[color]}`}>
          <i className={`${icon} fa-lg`}></i>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
