import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

const RecentVideos: React.FC = () => {
  const { videos, loading } = useSelector((state: RootState) => state.videos);

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      published: 'bg-green-100 text-green-800',
      processing: 'bg-yellow-100 text-yellow-800',
      private: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClasses[status as keyof typeof statusClasses] || statusClasses.private}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-4 border-b">
          <h2 className="text-xl font-semibold text-gray-800">Recently Added Videos</h2>
        </div>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="p-4 border-b">
        <h2 className="text-xl font-semibold text-gray-800">Recently Added Videos</h2>
      </div>
      <div className="overflow-x-auto">
        {videos.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <i className="fas fa-video text-4xl mb-4"></i>
            <p>No videos found</p>
          </div>
        ) : (
          <table className="min-w-full">
            <tbody className="bg-white">
              {videos.slice(0, 5).map((video) => (
                <tr key={video.id} className="border-b border-gray-200 hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-20 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                        {video.thumbnail ? (
                          <img
                            className="w-20 h-12 object-cover rounded-md"
                            src={video.thumbnail}
                            alt="Video thumbnail"
                          />
                        ) : (
                          <i className="fas fa-play text-gray-400"></i>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{video.title}</div>
                        <div className="text-sm text-gray-500">
                          {video.category?.name || 'Uncategorized'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(video.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(video.status)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default RecentVideos;
