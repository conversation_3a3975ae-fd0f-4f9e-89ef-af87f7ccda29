import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { VideoWithDetails, CreateVideoRequest, UpdateVideoRequest, VideoStats } from '../types';
import { videosApi } from '../api/videos';

interface VideosState {
  videos: VideoWithDetails[];
  currentVideo: VideoWithDetails | null;
  stats: VideoStats | null;
  loading: boolean;
  error: string | null;
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    limit: number;
  } | null;
}

const initialState: VideosState = {
  videos: [],
  currentVideo: null,
  stats: null,
  loading: false,
  error: null,
  pagination: null,
};

// Async thunks
export const fetchVideos = createAsyncThunk(
  'videos/fetchVideos',
  async (params: {
    page?: number;
    limit?: number;
    category_id?: number;
    status?: string;
    user_id?: number;
  } = {}, { rejectWithValue }) => {
    try {
      const response = await videosApi.getVideos(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch videos');
    }
  }
);

export const fetchVideo = createAsyncThunk(
  'videos/fetchVideo',
  async (id: number, { rejectWithValue }) => {
    try {
      const video = await videosApi.getVideo({ id });
      return video;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch video');
    }
  }
);

export const createVideo = createAsyncThunk(
  'videos/createVideo',
  async (data: CreateVideoRequest, { rejectWithValue }) => {
    try {
      const video = await videosApi.createVideo(data);
      return video;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to create video');
    }
  }
);

export const updateVideo = createAsyncThunk(
  'videos/updateVideo',
  async ({ id, data }: { id: number; data: UpdateVideoRequest }, { rejectWithValue }) => {
    try {
      const video = await videosApi.updateVideo(id, data);
      return video;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to update video');
    }
  }
);

export const deleteVideo = createAsyncThunk(
  'videos/deleteVideo',
  async (id: number, { rejectWithValue }) => {
    try {
      await videosApi.deleteVideo(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to delete video');
    }
  }
);

export const uploadVideo = createAsyncThunk(
  'videos/uploadVideo',
  async ({ id, file }: { id: number; file: File }, { rejectWithValue }) => {
    try {
      await videosApi.uploadVideo(id, file);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to upload video');
    }
  }
);

export const fetchVideoStats = createAsyncThunk(
  'videos/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      const stats = await videosApi.getStats();
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.error || 'Failed to fetch video stats');
    }
  }
);

// Videos slice
const videosSlice = createSlice({
  name: 'videos',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentVideo: (state) => {
      state.currentVideo = null;
    },
    incrementViews: (state, action: PayloadAction<number>) => {
      const videoId = action.payload;
      const video = state.videos.find(v => v.id === videoId);
      if (video) {
        video.views += 1;
      }
      if (state.currentVideo && state.currentVideo.id === videoId) {
        state.currentVideo.views += 1;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch videos
      .addCase(fetchVideos.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVideos.fulfilled, (state, action) => {
        state.loading = false;
        state.videos = action.payload.data.videos || [];
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchVideos.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch single video
      .addCase(fetchVideo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVideo.fulfilled, (state, action) => {
        state.loading = false;
        state.currentVideo = action.payload;
      })
      .addCase(fetchVideo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create video
      .addCase(createVideo.fulfilled, (state, action) => {
        state.videos.unshift(action.payload);
      })
      // Update video
      .addCase(updateVideo.fulfilled, (state, action) => {
        const index = state.videos.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.videos[index] = action.payload;
        }
        if (state.currentVideo && state.currentVideo.id === action.payload.id) {
          state.currentVideo = action.payload;
        }
      })
      // Delete video
      .addCase(deleteVideo.fulfilled, (state, action) => {
        state.videos = state.videos.filter(v => v.id !== action.payload);
        if (state.currentVideo && state.currentVideo.id === action.payload) {
          state.currentVideo = null;
        }
      })
      // Fetch stats
      .addCase(fetchVideoStats.fulfilled, (state, action) => {
        state.stats = action.payload;
      });
  },
});

export const { clearError, clearCurrentVideo, incrementViews } = videosSlice.actions;
export default videosSlice.reducer;
