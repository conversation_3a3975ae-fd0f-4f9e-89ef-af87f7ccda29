{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss7-compat": "^2.2.17", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/react-router-dom": "^5.3.3", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.7.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "vite", "build": "tsc && vite build", "serve": "vite preview", "test": "jest"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-react": "^0.6.2", "@types/react": "^19.1.9", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.1", "tailwindcss": "^4.1.11", "vite": "^5.4.19"}}