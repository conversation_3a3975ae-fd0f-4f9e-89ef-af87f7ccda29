package main

import (
	"log"
	"os"
	"strings"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"damoncoo/coremovie/database"
	"damoncoo/coremovie/routes"
	"damoncoo/coremovie/services"

	godotenv "github.com/joho/godotenv"
)

func main() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Fatalf("Error loading .env file: %v", err)
	}

	// Initialize database
	if err := database.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.Close()

	// Run database migrations
	if err := database.Migrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// Seed initial data
	if err := database.SeedData(); err != nil {
		log.Fatalf("Failed to seed database: %v", err)
	}

	// Set Gin mode
	ginMode := os.Getenv("GIN_MODE")
	if ginMode == "" {
		ginMode = "debug"
	}
	gin.SetMode(ginMode)

	// Create Gin router
	router := gin.Default()

	// Configure CORS
	config := cors.DefaultConfig()
	config.AllowOrigins = strings.Split(os.Getenv("ALLOW_ORIGINS"), ",")
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Accept",
		"Authorization",
		"X-Requested-With",
	}
	config.AllowCredentials = true
	router.Use(cors.New(config))

	// API routes group
	api := router.Group("/api/v1")
	root := router.Group("/")

	// Register routes
	userService := services.NewUserService()
	userRoutes := routes.NewUserRoutes(userService)
	userRoutes.RegisterRoutes(api)

	categoryRoutes := routes.NewCategoryRoutes()
	categoryRoutes.RegisterRoutes(api)

	videoRoutes := routes.NewVideoRoutes()
	videoRoutes.RegisterRoutes(api, root)

	// Episode routes
	episodeService := services.NewEpisodeService()
	episodeRoutes := routes.NewEpisodeRoutes(episodeService)
	episodeRoutes.SetupRoutes(router)

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "CoreMovie API is running",
		})
	})

	// Get port from environment or use default
	port := os.Getenv("PORT")
	log.Printf("Starting server on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}