package storage

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"

	"damoncoo/coremovie/utils"
)

// StorageType represents the type of storage to use
type StorageType string

const (
	LocalStorage StorageType = "local"
	S3Storage    StorageType = "s3"
)

// FileMetadata contains metadata about an uploaded file
type FileMetadata struct {
	Filename    string
	Filepath    string
	Size        int64
	ContentType string
	URL         string
}

// PresignedURLResponse represents a response with presigned URL
type PresignedURLResponse struct {
	URL      string `json:"url"`
	FilePath string `json:"file_path"`
}

// Storage defines the interface for file storage operations
type Storage interface {
	// UploadFile uploads a file and returns its metadata
	UploadFile(file multipart.File, fileHeader *multipart.FileHeader, videoID int64) (*FileMetadata, error)

	// GeneratePresignedURL generates a presigned URL for direct upload
	GeneratePresignedURL(videoID int64, filename string, filesize int64, filetype string) (*PresignedURLResponse, error)

	// DeleteFile deletes a file by its path
	DeleteFile(filepath string) error

	// GetFileURL returns the URL to access a file
	GetFileURL(filepath string) string

	// Type returns the storage type
	Type() StorageType
}

// LocalStorageImpl implements Storage for local file system storage
type LocalStorageImpl struct {
	UploadPath      string
	BaseURL         string
	signedURLConfig *utils.SignedURLConfig
}

// NewLocalStorage creates a new local storage instance
func NewLocalStorage(uploadPath, baseURL string) *LocalStorageImpl {
	return &LocalStorageImpl{
		UploadPath:      uploadPath,
		BaseURL:         baseURL,
		signedURLConfig: utils.NewSignedURLConfig(),
	}
}

// UploadFile uploads a file to the local file system
func (s *LocalStorageImpl) UploadFile(file multipart.File, fileHeader *multipart.FileHeader, videoID int64) (*FileMetadata, error) {
	// Create upload directory if it doesn't exist
	if err := os.MkdirAll(s.UploadPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %v", err)
	}

	// Generate unique filename with full path structure to match what GeneratePresignedURL would create
	ext := filepath.Ext(fileHeader.Filename)
	date := time.Now().Format("2006/01/02")
	filename := fmt.Sprintf("veotex/videos/%s/%d/%d_%d%s", date, videoID, videoID, time.Now().Unix(), ext)
	filePath := filepath.Join(s.UploadPath, filename)

	// Create directory structure if it doesn't exist
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory structure: %v", err)
	}

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %v", err)
	}
	defer dst.Close()

	// Copy file content
	_, err = io.Copy(dst, file)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file: %v", err)
	}

	// Create file metadata
	metadata := &FileMetadata{
		Filename:    fileHeader.Filename,
		Filepath:    fmt.Sprintf("%s/uploads/%s", s.BaseURL, filename),
		Size:        fileHeader.Size,
		ContentType: fileHeader.Header.Get("Content-Type"),
		URL:         fmt.Sprintf("%s/%s", s.BaseURL, filename),
	}

	return metadata, nil
}

// GeneratePresignedURL generates a presigned URL for direct upload to local storage
func (s *LocalStorageImpl) GeneratePresignedURL(videoID int64, filename string, filesize int64, filetype string) (*PresignedURLResponse, error) {
	// Generate unique file path
	ext := filepath.Ext(filename)
	date := time.Now().Format("2006/01/02")
	filePath := fmt.Sprintf("veotex/videos/%s/%d/%d_%d%s", date, videoID, videoID, time.Now().Unix(), ext)

	// Create signed URL parameters
	params := &utils.SignedURLParams{
		VideoID:  videoID,
		Filename: filename,
		FileSize: filesize,
		FileType: filetype,
		FilePath: filePath,
	}

	// Generate the signed URL
	signedURL, err := s.signedURLConfig.GenerateSignedURL(params)
	if err != nil {
		return nil, fmt.Errorf("failed to generate signed URL: %v", err)
	}

	return &PresignedURLResponse{
		URL:      signedURL,
		FilePath: fmt.Sprintf("%s/uploads/%s", s.BaseURL, filePath),
	}, nil
}

// DeleteFile deletes a file from the local file system
func (s *LocalStorageImpl) DeleteFile(filepath string) error {
	if filepath == "" {
		return nil
	}

	if err := os.Remove(filepath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete file: %v", err)
	}

	return nil
}

// GetFileURL returns the URL to access a local file
func (s *LocalStorageImpl) GetFileURL(filepath string) string {
	// Extract just the filename from the full path
	filename := filepath[len(s.UploadPath)+1:] // +1 to remove the leading slash
	return fmt.Sprintf("%s/%s", s.BaseURL, filename)
}

// Type returns the storage type
func (s *LocalStorageImpl) Type() StorageType {
	return LocalStorage
}
