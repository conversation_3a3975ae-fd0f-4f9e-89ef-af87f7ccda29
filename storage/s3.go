package storage

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
)

// S3Config holds S3 configuration
type S3Config struct {
	Endpoint        string
	Region          string
	AccessKeyID     string
	SecretAccessKey string
	BucketName      string
	UseSSL          bool
	AccessURL       string
}

// S3StorageImpl implements Storage for S3-compatible storage
type S3StorageImpl struct {
	config     S3Config
	s3Client   *s3.S3
	uploader   *s3manager.Uploader
	bucketName string
	baseURL    string
}

// NewS3Storage creates a new S3 storage instance
func NewS3Storage(config S3Config) (*S3StorageImpl, error) {
	// Create AWS session
	awsConfig := &aws.Config{
		Region:   aws.String(config.Region),
		Endpoint: aws.String(config.Endpoint),
		Credentials: credentials.NewStaticCredentials(
			config.AccessKeyID,
			config.SecretAccessKey,
			"", // Token not needed for static credentials
		),
		DisableSSL:       aws.Bool(!config.UseSSL),
		S3ForcePathStyle: aws.Bool(true), // Required for S3-compatible services like MinIO
	}

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %v", err)
	}

	s3Client := s3.New(sess)
	uploader := s3manager.NewUploader(sess)

	// Test connection
	_, err = s3Client.HeadBucket(&s3.HeadBucketInput{
		Bucket: aws.String(config.BucketName),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to S3 bucket: %v", err)
	}

	return &S3StorageImpl{
		config:     config,
		s3Client:   s3Client,
		uploader:   uploader,
		bucketName: config.BucketName,
		baseURL:    fmt.Sprintf("%s/%s", config.Endpoint, config.BucketName),
	}, nil
}

// UploadFile uploads a file to S3-compatible storage
func (s *S3StorageImpl) UploadFile(file multipart.File, fileHeader *multipart.FileHeader, videoID int64) (*FileMetadata, error) {
	// Read file content into memory
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %v", err)
	}

	// Generate unique key (path) for the file
	ext := filepath.Ext(fileHeader.Filename)
	date := time.Now().Format("2006/01/02")
	key := fmt.Sprintf("/veotex/videos/%s/%d/%d_%d%s", date, videoID, videoID, time.Now().Unix(), ext)

	// Upload to S3
	result, err := s.uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(key),
		Body:        bytes.NewReader(fileContent),
		ContentType: aws.String(fileHeader.Header.Get("Content-Type")),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to upload file to S3: %v", err)
	}

	// Create file metadata
	metadata := &FileMetadata{
		Filename:    fileHeader.Filename,
		Filepath:    key,
		Size:        fileHeader.Size,
		ContentType: fileHeader.Header.Get("Content-Type"),
		URL:         result.Location,
	}

	return metadata, nil
}

// GeneratePresignedURL generates a presigned URL for direct S3 upload
func (s *S3StorageImpl) GeneratePresignedURL(videoID int64, filename string, filesize int64, filetype string) (*PresignedURLResponse, error) {
	// Generate key (path) for the file
	ext := filepath.Ext(filename)
	date := time.Now().Format("2006/01/02")
	key := fmt.Sprintf("/veotex/videos/%s/%d/%d_%d%s", date, videoID, videoID, time.Now().Unix(), ext)

	// Generate presigned URL for PUT request
	req, _ := s.s3Client.PutObjectRequest(&s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(key),
		ContentType: aws.String(filetype),
	})

	url, err := req.Presign(15 * time.Minute) // URL expires in 15 minutes
	if err != nil {
		return nil, fmt.Errorf("failed to generate presigned URL: %v", err)
	}

	return &PresignedURLResponse{
		URL:      url,
		FilePath: fmt.Sprintf("%s%s", s.config.AccessURL, key),
	}, nil
}

// DeleteFile deletes a file from S3-compatible storage
func (s *S3StorageImpl) DeleteFile(filepath string) error {
	if filepath == "" {
		return nil
	}

	_, err := s.s3Client.DeleteObject(&s3.DeleteObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(filepath),
	})
	if err != nil {
		return fmt.Errorf("failed to delete file from S3: %v", err)
	}

	// Wait for the object to be deleted
	err = s.s3Client.WaitUntilObjectNotExists(&s3.HeadObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(filepath),
	})
	if err != nil {
		return fmt.Errorf("failed waiting for object deletion: %v", err)
	}

	return nil
}

// GetFileURL returns the URL to access a file in S3
func (s *S3StorageImpl) GetFileURL(filepath string) string {
	if filepath == "" {
		return ""
	}

	// Generate a presigned URL that expires in 1 hour
	req, _ := s.s3Client.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(filepath),
	})

	url, err := req.Presign(1 * time.Hour)
	if err != nil {
		// Fallback to direct URL if presigning fails
		return fmt.Sprintf("%s/%s/%s", s.config.Endpoint, s.bucketName, filepath)
	}

	return url
}

// Type returns the storage type
func (s *S3StorageImpl) Type() StorageType {
	return S3Storage
}
